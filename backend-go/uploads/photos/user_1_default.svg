<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#84cc16;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#65a30d;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="faceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background with Gradient -->
  <circle cx="100" cy="100" r="100" fill="url(#avatarGradient)"/>
  
  <!-- User Icon -->
  <g transform="translate(100, 100)">
    <!-- Head -->
    <circle cx="0" cy="-20" r="25" fill="url(#faceGradient)" stroke="#e2e8f0" stroke-width="2"/>
    
    <!-- Body -->
    <path d="M -35 20 Q -35 0 -25 0 L 25 0 Q 35 0 35 20 L 35 50 L -35 50 Z" 
          fill="url(#faceGradient)" stroke="#e2e8f0" stroke-width="2"/>
    
    <!-- Eyes -->
    <circle cx="-8" cy="-25" r="2" fill="#64748b"/>
    <circle cx="8" cy="-25" r="2" fill="#64748b"/>
    
    <!-- Smile -->
    <path d="M -8 -15 Q 0 -10 8 -15" stroke="#64748b" stroke-width="2" fill="none" stroke-linecap="round"/>
    
    <!-- Initial Letter -->
    <text x="0" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#64748b">J</text>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="160" cy="40" r="3" fill="#ffffff" opacity="0.3"/>
  <circle cx="40" cy="160" r="2" fill="#ffffff" opacity="0.4"/>
  <circle cx="170" cy="170" r="2" fill="#ffffff" opacity="0.2"/>
</svg>