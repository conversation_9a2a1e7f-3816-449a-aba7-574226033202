package user

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"vpn-shop/backend-go/db"
	account "vpn-shop/backend-go/models/account"
	payment "vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/shared"
	userModel "vpn-shop/backend-go/models/user"

	"golang.org/x/crypto/bcrypt"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"

	"gorm.io/gorm"
)

// SwaggerRoleResponse is used to explicitly define the Role structure for Swagger
type SwaggerRoleResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// UserResponse defines the essential user profile data for API responses.
type UserResponse struct {
	ID               uint                  `json:"id"`
	Username         string                `json:"username"`
	Name             string                `json:"name"`
	Email            string                `json:"email"`
	Saldo            int64                 `json:"saldo"`
	Whatsapp         string                `json:"whatsapp"`
	EmailVerified    bool                  `json:"email_verified"`
	WhatsappVerified bool                  `json:"whatsapp_verified"`
	AccountType      string                `json:"account_type"`
	EmailVerifiedAt  *time.Time            `json:"email_verified_at"`
	Roles            []SwaggerRoleResponse `json:"roles"`
}

// UserStatsResponse defines the user's aggregated statistics.
type UserStatsResponse struct {
	PayBulanan       int64 `json:"pay_bulanan"`
	TotalPay         int64 `json:"total_pay"`
	BatasTrial       int   `json:"batas_trial"`
	Trial            int   `json:"trial"`
	JumlahAkunTrial  int64 `json:"jumlah_akun_trial"`
	JumlahAkunHourly int64 `json:"jumlah_akun_hourly"`
	JumlahAkunMonth  int64 `json:"jumlah_akun_month"`
	JumlahAkunTrojan int64 `json:"jumlah_akun_trojan"`
	JumlahAkunVmess  int64 `json:"jumlah_akun_vmess"`
	JumlahAkunVless  int64 `json:"jumlah_akun_vless"`
	JumlahAkunSsh    int64 `json:"jumlah_akun_ssh"`
	TotalAccount     int64 `json:"total_account"`
}

// SwaggerProfileResponse is used to explicitly define the ProfileResponse structure for Swagger
type SwaggerProfileResponse struct {
	ID               uint                  `json:"id"`
	Username         string                `json:"username"`
	Name             string                `json:"name"`
	Email            string                `json:"email"`
	Saldo            int64                 `json:"saldo"`
	PayBulanan       int64                 `json:"pay_bulanan"`
	TotalPay         int64                 `json:"total_pay"`
	BatasTrial       int                   `json:"batas_trial"`
	Trial            int                   `json:"trial"`
	JumlahAkunTrial  int64                 `json:"jumlah_akun_trial"`
	JumlahAkunHourly int64                 `json:"jumlah_akun_hourly"`
	JumlahAkunMonth  int64                 `json:"jumlah_akun_month"`
	JumlahAkunTrojan int64                 `json:"jumlah_akun_trojan"`
	JumlahAkunVmess  int64                 `json:"jumlah_akun_vmess"`
	JumlahAkunVless  int64                 `json:"jumlah_akun_vless"`
	JumlahAkunSsh    int64                 `json:"jumlah_akun_ssh"`
	TotalAccount     int64                 `json:"total_account"`
	Whatsapp         string                `json:"whatsapp"`
	EmailVerified    bool                  `json:"email_verified"`
	WhatsappVerified bool                  `json:"whatsapp_verified"`
	AccountType      string                `json:"account_type"`
	EmailVerifiedAt  *time.Time            `json:"email_verified_at"`
	Roles            []SwaggerRoleResponse `json:"roles"`
}

// ProfileResponse is a legacy struct, replaced by UserResponse and UserStatsResponse.
// It is kept temporarily to avoid breaking changes in other parts of the code.
type ProfileResponse struct {
	ID               uint              `json:"id"`
	Username         string            `json:"username"`
	Name             string            `json:"name"`
	Email            string            `json:"email"`
	Saldo            int64             `json:"saldo"`
	PayBulanan       int64             `json:"pay_bulanan"`
	TotalPay         int64             `json:"total_pay"`
	BatasTrial       int               `json:"batas_trial"`
	Trial            int               `json:"trial"`
	JumlahAkunTrial  int64             `json:"jumlah_akun_trial"`
	JumlahAkunHourly int64             `json:"jumlah_akun_hourly"`
	JumlahAkunMonth  int64             `json:"jumlah_akun_month"`
	JumlahAkunTrojan int64             `json:"jumlah_akun_trojan"`
	JumlahAkunVmess  int64             `json:"jumlah_akun_vmess"`
	JumlahAkunVless  int64             `json:"jumlah_akun_vless"`
	JumlahAkunSsh    int64             `json:"jumlah_akun_ssh"`
	TotalAccount     int64             `json:"total_account"`
	Whatsapp         string            `json:"whatsapp"`
	EmailVerified    bool              `json:"email_verified"`
	WhatsappVerified bool              `json:"whatsapp_verified"`
	AccountType      string            `json:"account_type"`
	EmailVerifiedAt  *time.Time        `json:"email_verified_at"`
	Roles            []*userModel.Role `json:"roles"`
}

// GetMe retrieves the authenticated user's profile.
// @Summary Get current user's profile
// @Description Get the profile of the currently logged-in user
// @Tags User
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} UserResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Router /users/me [get]
func GetMe(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userID, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}

	var user userModel.User
	if result := db.DB.Preload("Roles").First(&user, uint(userID)); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	email := ""
	if user.Email != nil {
		email = *user.Email
	}

	whatsapp := ""
	if user.Whatsapp != nil {
		whatsapp = *user.Whatsapp
	}

	// Determine account type based on roles for better accuracy
	accountType := "Member" // Default value
	isAdmin := false
	isReseller := false
	for _, role := range user.Roles {
		if role.Name == "admin" {
			isAdmin = true
			break // Admin role has the highest priority
		}
		if role.Name == "reseller" {
			isReseller = true
		}
	}

	if isAdmin {
		accountType = "Admin"
	} else if isReseller {
		accountType = "Reseller"
	}

	// Convert []*userModel.Role to []SwaggerRoleResponse for consistent output
	var rolesResponse []SwaggerRoleResponse
	for _, r := range user.Roles {
		var desc string
		if r.Description != nil {
			desc = *r.Description
		}
		rolesResponse = append(rolesResponse, SwaggerRoleResponse{
			ID:          r.ID,
			Name:        r.Name,
			Description: desc,
			CreatedAt:   r.CreatedAt,
			UpdatedAt:   r.UpdatedAt,
		})
	}

	response := UserResponse{
		ID:               user.ID,
		Username:         user.Username,
		Name:             user.Name,
		Email:            email,
		Saldo:            user.Saldo,
		Whatsapp:         whatsapp,
		EmailVerified:    user.EmailVerified == 1,
		WhatsappVerified: user.VerifWa == 1,
		AccountType:      accountType,
		EmailVerifiedAt:  user.EmailVerifiedAt,
		Roles:            rolesResponse,
	}

	return c.JSON(http.StatusOK, response)
}

// GetUserStats retrieves aggregated statistics for the authenticated user.
// @Summary Get current user's statistics
// @Description Get aggregated statistics like account counts for the currently logged-in user
// @Tags User
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} UserStatsResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Router /users/me/stats [get]
func GetUserStats(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userID, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}

	var user userModel.User
	if result := db.DB.First(&user, uint(userID)); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	batasTrial := 0
	if user.BatasTrial != nil {
		batasTrial = *user.BatasTrial
	}

	trialUsed := 0
	if user.Trial != nil {
		trialUsed = *user.Trial
	}

	// Convert user ID to string for account queries
	userIDStr := fmt.Sprintf("%d", user.ID)

	// Count accounts by protocol type
	var jumlahTrojan, jumlahVmess, jumlahVless, jumlahSsh int64
	db.DB.Model(&account.AccountTrojan{}).Where("user_id = ?", userIDStr).Count(&jumlahTrojan)
	db.DB.Model(&account.AccountVmess{}).Where("user_id = ?", userIDStr).Count(&jumlahVmess)
	db.DB.Model(&account.AccountVless{}).Where("user_id = ?", userIDStr).Count(&jumlahVless)
	db.DB.Model(&account.AccountSsh{}).Where("user_id = ?", userIDStr).Count(&jumlahSsh)

	// Count accounts by subscription type
	var jumlahTrial, jumlahHourly, jumlahMonthly int64
	var count int64

	// Count trial
	db.DB.Model(&account.AccountTrojan{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&count)
	jumlahTrial += count
	db.DB.Model(&account.AccountVmess{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&count)
	jumlahTrial += count
	db.DB.Model(&account.AccountVless{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&count)
	jumlahTrial += count
	db.DB.Model(&account.AccountSsh{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "trial").Count(&count)
	jumlahTrial += count

	// Count hourly
	db.DB.Model(&account.AccountTrojan{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "hourly").Count(&count)
	jumlahHourly += count
	db.DB.Model(&account.AccountVmess{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "hourly").Count(&count)
	jumlahHourly += count
	db.DB.Model(&account.AccountVless{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "hourly").Count(&count)
	jumlahHourly += count
	db.DB.Model(&account.AccountSsh{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "hourly").Count(&count)
	jumlahHourly += count

	// Count monthly
	db.DB.Model(&account.AccountTrojan{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "monthly").Count(&count)
	jumlahMonthly += count
	db.DB.Model(&account.AccountVmess{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "monthly").Count(&count)
	jumlahMonthly += count
	db.DB.Model(&account.AccountVless{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "monthly").Count(&count)
	jumlahMonthly += count
	db.DB.Model(&account.AccountSsh{}).Where("user_id = ? AND subscription_type = ?", userIDStr, "monthly").Count(&count)
	jumlahMonthly += count

	// Calculate total accounts
	totalAccount := jumlahTrojan + jumlahVmess + jumlahVless + jumlahSsh

	response := UserStatsResponse{
		PayBulanan:       user.PayBulanan,
		TotalPay:         user.TotalPay,
		BatasTrial:       batasTrial,
		Trial:            trialUsed,
		JumlahAkunTrial:  jumlahTrial,
		JumlahAkunHourly: jumlahHourly,
		JumlahAkunMonth:  jumlahMonthly,
		JumlahAkunTrojan: jumlahTrojan,
		JumlahAkunVmess:  jumlahVmess,
		JumlahAkunVless:  jumlahVless,
		JumlahAkunSsh:    jumlahSsh,
		TotalAccount:     totalAccount,
	}

	return c.JSON(http.StatusOK, response)
}

// UpdateProfileRequest defines the request body for updating a user's own profile.
type UpdateProfileRequest struct {
	Name     *string `json:"name"`
	Email    *string `json:"email"`
	Whatsapp *string `json:"whatsapp"`
}

// UpdateMe updates the authenticated user's profile.
// @Summary Update current user's profile
// @Description Update the profile of the currently logged-in user
// @Tags User
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param user body UpdateProfileRequest true "User data to update"
// @Success 200 {object} UserResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 409 {object} shared.ErrorResponse "Email already in use"
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me [put]
func UpdateMe(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}
	userID := uint(userIDFloat)

	var user userModel.User
	// Preload roles to include them in the response
	if result := db.DB.Preload("Roles").First(&user, userID); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	var req UpdateProfileRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	// Validate and update email if provided
	if req.Email != nil {
		emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailRegex.MatchString(*req.Email) {
			return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format email tidak valid"})
		}

		// Check if email is already taken by another user
		var existingUser userModel.User
		if err := db.DB.Where("email = ? AND id != ?", *req.Email, userID).First(&existingUser).Error; err == nil {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Email sudah digunakan oleh akun lain"})
		}

		// If email is changed, reset verification status
		if user.Email == nil || *user.Email != *req.Email {
			user.EmailVerified = 0 // Use 0 for false
			user.EmailVerifiedAt = nil
		}
		user.Email = req.Email
	}

	// Update name if provided
	if req.Name != nil {
		user.Name = *req.Name
	}

	// Update whatsapp if provided
	if req.Whatsapp != nil {
		user.Whatsapp = req.Whatsapp
	}
	// Saldo is intentionally not updatable via this endpoint

	if result := db.DB.Save(&user); result.Error != nil {
		log.Printf("Tidak dapat memperbarui pengguna %d: %v", userID, result.Error)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui profil pengguna"})
	}

	// Build the response
	email := ""
	if user.Email != nil {
		email = *user.Email
	}
	whatsapp := ""
	if user.Whatsapp != nil {
		whatsapp = *user.Whatsapp
	}

	// Determine account type based on roles for better accuracy
	accountType := "Member" // Default value
	isAdmin := false
	isReseller := false
	for _, role := range user.Roles {
		if role.Name == "admin" {
			isAdmin = true
			break // Admin role has the highest priority
		}
		if role.Name == "reseller" {
			isReseller = true
		}
	}

	if isAdmin {
		accountType = "Admin"
	} else if isReseller {
		accountType = "Reseller"
	}

	// Convert []*userModel.Role to []SwaggerRoleResponse for consistent output
	var rolesResponse []SwaggerRoleResponse
	for _, r := range user.Roles {
		var desc string
		if r.Description != nil {
			desc = *r.Description
		}
		rolesResponse = append(rolesResponse, SwaggerRoleResponse{
			ID:          r.ID,
			Name:        r.Name,
			Description: desc,
			CreatedAt:   r.CreatedAt,
			UpdatedAt:   r.UpdatedAt,
		})
	}

	response := UserResponse{
		ID:               user.ID,
		Username:         user.Username,
		Name:             user.Name,
		Email:            email,
		Saldo:            user.Saldo,
		Whatsapp:         whatsapp,
		EmailVerified:    user.EmailVerified == 1,
		WhatsappVerified: user.VerifWa == 1,
		AccountType:      accountType,
		EmailVerifiedAt:  user.EmailVerifiedAt,
		Roles:            rolesResponse,
	}

	return c.JSON(http.StatusOK, response)
}

// ChangePasswordRequest defines the request body for changing password.
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

// ChangePassword handles the password change process for the authenticated user.
// @Summary Change user's password
// @Description Allows the authenticated user to change their password
// @Tags User
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param passwords body ChangePasswordRequest true "Old and new passwords"
// @Success 200 {object} map[string]string
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/change-password [put]
func ChangePassword(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}
	userID := uint(userIDFloat)

	var req ChangePasswordRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	var user userModel.User
	if result := db.DB.First(&user, userID); result.Error != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	// Compare the old password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Password lama tidak sesuai"})
	}

	// Hash the new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengenkripsi password baru"})
	}

	user.Password = string(hashedPassword)
	if result := db.DB.Save(&user); result.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui password"})
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Password berhasil diubah"})
}

// PaginatedInvoicesResponse defines the structure for paginated invoice results.
type PaginatedInvoicesResponse struct {
	Total int64                 `json:"total"`
	Page  int                   `json:"page"`
	Limit int                   `json:"limit"`
	Data  []payment.Transaction `json:"data"`
}

// GetUserInvoices retrieves all invoices for the authenticated user.
// @Summary Get user's invoices
// @Description Get a list of all invoices for the currently logged-in user
// @Tags User
// @Security BearerAuth
// @Produce  json
// @Param page query int false "Page number for pagination" default(1)
// @Param limit query int false "Number of items per page" default(10)
// @Success 200 {object} PaginatedInvoicesResponse
// @Failure 401 {object} shared.ErrorResponse "Unauthorized"
// @Failure 500 {object} shared.ErrorResponse "Internal Server Error"
// @Router /users/me/invoices [get]
func GetUserInvoices(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}
	userID := uint(userIDFloat)

	// Pagination
	page, _ := strconv.Atoi(c.QueryParam("page"))
	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	offset := (page - 1) * limit

	var transactions []payment.Transaction
	var total int64

	// Get total count first
	db.DB.Model(&payment.Transaction{}).Where("user_id = ?", userID).Count(&total)

	// Then get the paginated data with preloaded user data
	if err := db.DB.Preload("User.Roles").Where("user_id = ?", userID).Order("created_at DESC").Offset(offset).Limit(limit).Find(&transactions).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data invoice"})
	}

	// Pastikan data user terisi dengan lengkap untuk setiap transaksi
	for i := range transactions {
		if transactions[i].UserID > 0 && transactions[i].User.ID == 0 {
			// Jika user ID ada tapi data user kosong, ambil data user secara manual
			var user userModel.User
			if err := db.DB.Preload("Roles").First(&user, transactions[i].UserID).Error; err == nil {
				transactions[i].User = user
			}
		}
	}

	response := PaginatedInvoicesResponse{
		Total: total,
		Page:  page,
		Limit: limit,
		Data:  transactions,
	}

	return c.JSON(http.StatusOK, response)
}

// GetUserInvoiceDetails retrieves detailed information for a specific invoice.
// @Summary Get User Invoice Details
// @Description Retrieves detailed information for a specific invoice for the authenticated user.
// @Tags User
// @Security BearerAuth
// @Produce json
// @Param invoice_id path string true "Invoice ID"
// @Success 200 {object} payment.Transaction
// @Failure 401 {object} shared.ErrorResponse "Unauthorized"
// @Failure 404 {object} shared.ErrorResponse "Invoice not found"
// @Failure 500 {object} shared.ErrorResponse "Internal server error"
// @Router /users/me/invoices/{invoice_id} [get]
func GetUserInvoiceDetails(c echo.Context) error {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna dalam token tidak valid"})
	}
	userID := uint(userIDFloat)
	invoiceID := c.Param("invoice_id")

	var transaction payment.Transaction
	if err := db.DB.Preload("User.Roles").Where("invoice_id = ? AND user_id = ?", invoiceID, userID).First(&transaction).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Invoice tidak ditemukan atau Anda tidak memiliki izin untuk melihatnya"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan saat mengambil data invoice"})
	}

	// Pastikan data user terisi dengan lengkap
	if transaction.UserID > 0 && transaction.User.ID == 0 {
		// Jika user ID ada tapi data user kosong, ambil data user secara manual
		var user userModel.User
		if err := db.DB.Preload("Roles").First(&user, transaction.UserID).Error; err == nil {
			transaction.User = user
		}
	}

	return c.JSON(http.StatusOK, transaction)
}

// UploadPhotoRequest defines the response for photo upload
type UploadPhotoResponse struct {
	Message  string `json:"message"`
	PhotoURL string `json:"photo_url"`
}

// UploadPhoto uploads a photo for the authenticated user.
// @Summary Upload user profile photo
// @Description Upload a profile photo for the currently logged-in user
// @Tags User
// @Security BearerAuth
// @Accept multipart/form-data
// @Produce json
// @Param photo formData file true "Photo file to upload"
// @Success 200 {object} UploadPhotoResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 413 {object} shared.ErrorResponse "File too large"
// @Failure 415 {object} shared.ErrorResponse "Unsupported file type"
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/photo [post]
func UploadPhoto(c echo.Context) error {
	// Get user ID from JWT token
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token tidak valid"})
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format token tidak valid"})
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "ID pengguna tidak valid"})
	}
	userID := uint(userIDFloat)

	// Get the uploaded file
	file, err := c.FormFile("photo")
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "File foto tidak ditemukan"})
	}

	// Validate file size (max 5MB)
	maxSize := int64(2 * 1024 * 1024) // 2MB
	if file.Size > maxSize {
		return c.JSON(http.StatusRequestEntityTooLarge, shared.ErrorResponse{Error: "Ukuran file terlalu besar. Maksimal 5MB"})
	}

	// Validate file type
	allowedTypes := map[string]bool{
		"image/jpeg": true,
		"image/jpg":  true,
		"image/png":  true,
		"image/webp": true,
	}

	src, err := file.Open()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuka file"})
	}
	defer src.Close()

	// Read first 512 bytes to detect content type
	buffer := make([]byte, 512)
	_, err = src.Read(buffer)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca file"})
	}

	// Reset file pointer to beginning
	src.Seek(0, 0)

	contentType := http.DetectContentType(buffer)
	if !allowedTypes[contentType] {
		return c.JSON(http.StatusUnsupportedMediaType, shared.ErrorResponse{Error: "Tipe file tidak didukung. Hanya JPEG, PNG, dan WebP yang diizinkan"})
	}

	// Create uploads directory if it doesn't exist
	uploadsDir := "uploads/photos"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		log.Printf("Gagal membuat direktori uploads: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyiapkan direktori upload"})
	}

	// Generate unique filename
	ext := filepath.Ext(file.Filename)
	if ext == "" {
		// Determine extension from content type
		switch contentType {
		case "image/jpeg":
			ext = ".jpg"
		case "image/png":
			ext = ".png"
		case "image/webp":
			ext = ".webp"
		default:
			ext = ".jpg"
		}
	}

	filename := fmt.Sprintf("user_%d_%d%s", userID, time.Now().Unix(), ext)
	filePath := filepath.Join(uploadsDir, filename)

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		log.Printf("Gagal membuat file tujuan: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan file"})
	}
	defer dst.Close()

	// Copy file content
	if _, err = io.Copy(dst, src); err != nil {
		log.Printf("Gagal menyalin file: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan file"})
	}

	// Get user from database
	var user userModel.User
	if err := db.DB.First(&user, userID).Error; err != nil {
		// Clean up uploaded file if user not found
		os.Remove(filePath)
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
	}

	// Delete old photo if exists
	if user.PathPhoto != nil && *user.PathPhoto != "" {
		oldPhotoPath := *user.PathPhoto
		// Only delete if it's in our uploads directory
		if strings.HasPrefix(oldPhotoPath, "uploads/") {
			if err := os.Remove(oldPhotoPath); err != nil {
				log.Printf("Gagal menghapus foto lama: %v", err)
				// Don't return error, just log it
			}
		}
	}

	// Update user's photo path in database
	user.PathPhoto = &filePath
	if err := db.DB.Save(&user).Error; err != nil {
		// Clean up uploaded file if database update fails
		os.Remove(filePath)
		log.Printf("Gagal memperbarui path foto pengguna: %v", err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui profil pengguna"})
	}

	// Return success response
	response := UploadPhotoResponse{
		Message:  "Foto profil berhasil diupload",
		PhotoURL: filePath,
	}

	return c.JSON(http.StatusOK, response)
}
