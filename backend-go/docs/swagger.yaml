basePath: /api/v1
definitions:
  account.RenewRequest:
    properties:
      account_id:
        type: integer
      account_type:
        enum:
        - trojan
        - vmess
        - vless
        - ssh
        type: string
      duration_months:
        minimum: 1
        type: integer
      metode:
        type: string
      pembayaran:
        enum:
        - SALDO
        - TRIPAY
        type: string
    required:
    - account_id
    - account_type
    - duration_months
    - metode
    - pembayaran
    type: object
  admin.AdminSetting:
    properties:
      daily_mt_time:
        description: Waktu maintenance harian dalam format "HH:MM".
        type: string
      expired_minutes:
        description: Durasi dalam menit sebelum akun dianggap kadaluwarsa.
        type: integer
      hourly_billing_interval:
        description: Interval penagihan per jam dalam menit.
        type: integer
      min_saldo:
        description: Saldo minimal yang harus dimiliki pengguna.
        type: integer
      min_top_up:
        description: Nominal minimal untuk sekali top-up member.
        type: integer
      min_top_up_reseller:
        description: Nominal minimal untuk sekali top-up reseller.
        type: integer
    type: object
  admin.CreateAnnouncementRequest:
    properties:
      isi:
        type: string
      judul:
        type: string
    required:
    - isi
    - judul
    type: object
  admin.RoleResponse:
    properties:
      name:
        type: string
    type: object
  admin.SwaggerUserDetailResponse:
    properties:
      account_type:
        type: string
      created_at:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      id:
        type: integer
      jumlah_akun_hourly:
        type: integer
      jumlah_akun_month:
        type: integer
      jumlah_akun_ssh:
        type: integer
      jumlah_akun_trial:
        type: integer
      jumlah_akun_trojan:
        type: integer
      jumlah_akun_vless:
        type: integer
      jumlah_akun_vmess:
        type: integer
      name:
        type: string
      roles:
        items:
          $ref: '#/definitions/admin.RoleResponse'
        type: array
      saldo:
        type: integer
      suspend:
        type: string
      total_account:
        type: integer
      username:
        type: string
      verif_wa:
        type: boolean
      whatsapp:
        type: string
    type: object
  admin.SwaggerUserListResponse:
    properties:
      users:
        items:
          $ref: '#/definitions/admin.SwaggerUserDetailResponse'
        type: array
    type: object
  admin.UpdateAnnouncementRequest:
    properties:
      isi:
        type: string
      judul:
        type: string
    required:
    - isi
    - judul
    type: object
  admin.UpdateUserRequest:
    properties:
      email:
        type: string
      name:
        type: string
      roles:
        description: Admin can update roles
        items:
          type: string
        type: array
      saldo:
        type: integer
      whatsapp:
        type: string
    type: object
  announcement.AnnouncementResponse:
    properties:
      created_at:
        type: string
      id:
        type: integer
      isi:
        type: string
      judul:
        type: string
      updated_at:
        type: string
    type: object
  auth.RegisterRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      name:
        example: John Doe
        type: string
      password:
        example: password123
        minLength: 6
        type: string
      username:
        example: johndoe
        type: string
      whatsapp:
        example: "************"
        minLength: 10
        type: string
    required:
    - email
    - name
    - password
    - username
    - whatsapp
    type: object
  auth.TelegramAuthRequest:
    description: Create a new user account
    properties:
      auth_date:
        type: integer
      first_name:
        type: string
      hash:
        type: string
      id:
        type: integer
      last_name:
        type: string
      photo_url:
        type: string
      username:
        type: string
    required:
    - auth_date
    - hash
    - id
    type: object
  payment.CreatePaymentRequest:
    properties:
      items:
        items:
          $ref: '#/definitions/payment.PurchaseItem'
        type: array
      payment_method:
        type: string
    type: object
  payment.Fee:
    properties:
      flat:
        type: integer
      percent:
        type: number
    type: object
  payment.PaymentChannel:
    properties:
      active:
        type: boolean
      code:
        type: string
      fee_customer:
        $ref: '#/definitions/payment.Fee'
      fee_merchant:
        $ref: '#/definitions/payment.Fee'
      group:
        type: string
      icon_url:
        type: string
      maximum_amount:
        type: integer
      maximum_fee:
        type: integer
      minimum_amount:
        type: integer
      minimum_fee:
        type: integer
      name:
        type: string
      total_fee:
        $ref: '#/definitions/payment.TotalFee'
      type:
        type: string
    type: object
  payment.PurchaseItem:
    properties:
      quantity:
        type: integer
      server_id:
        type: integer
    type: object
  payment.TopUpRequest:
    properties:
      amount:
        type: integer
      payment_method:
        type: string
    type: object
  payment.TotalFee:
    properties:
      flat:
        type: integer
      percent:
        type: string
    type: object
  payment.Transaction:
    properties:
      account_id:
        type: integer
      account_type:
        type: string
      amount:
        type: integer
      created_at:
        type: string
      description:
        type: string
      duration:
        type: string
      gateway_checkout_url:
        type: string
      gateway_reference:
        type: string
      id:
        type: integer
      invoice_id:
        type: string
      payment_gateway:
        type: string
      status:
        $ref: '#/definitions/payment.TransactionStatus'
      type:
        $ref: '#/definitions/payment.TransactionType'
      updated_at:
        type: string
      user:
        $ref: '#/definitions/user.User'
      user_id:
        type: integer
    type: object
  payment.TransactionStatus:
    enum:
    - PENDING
    - SUCCESS
    - FAILED
    - EXPIRED
    type: string
    x-enum-varnames:
    - Pending
    - Success
    - Failed
    - Expired
  payment.TransactionType:
    enum:
    - TOPUP
    - PURCHASE_MONTHLY
    - PURCHASE_HOURLY
    - TRIAL
    - RENEWAL
    - REFUND
    - BILLING
    - BILLED_HOURLY
    type: string
    x-enum-varnames:
    - Topup
    - PurchaseMonthly
    - PurchaseHourly
    - Trial
    - Renewal
    - Refund
    - Billing
    - BilledHourly
  payment.TripayChannelsResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/payment.PaymentChannel'
        type: array
      message:
        type: string
      success:
        type: boolean
    type: object
  payment.TripayTransactionStatus:
    properties:
      data:
        properties:
          amount:
            type: integer
          amount_received:
            type: integer
          callback_url:
            type: string
          customer_email:
            type: string
          customer_name:
            type: string
          customer_phone:
            type: string
          fee:
            type: integer
          merchant_ref:
            type: string
          note:
            type: string
          paid_at:
            type: integer
          payment_method:
            type: string
          payment_method_name:
            type: string
          reference:
            type: string
          return_url:
            type: string
          status:
            type: string
          total_fee:
            type: integer
        type: object
      message:
        type: string
      success:
        type: boolean
    type: object
  public.TransactionResponse:
    properties:
      amount:
        type: integer
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      invoice_id:
        type: string
      user_avatar:
        type: string
      user_name:
        type: string
    type: object
  purchase.PurchaseHourlyRequest:
    properties:
      kode_server:
        type: string
      protocol:
        enum:
        - trojan
        - vmess
        - vless
        type: string
      username:
        type: string
    required:
    - kode_server
    - protocol
    - username
    type: object
  purchase.PurchaseMonthlyRequest:
    properties:
      bulan:
        maximum: 12
        minimum: 1
        type: integer
      kode_server:
        type: string
      metode:
        type: string
      pembayaran:
        enum:
        - SALDO
        - TRIPAY
        type: string
      protocol:
        enum:
        - trojan
        - vmess
        - vless
        type: string
      username:
        maxLength: 12
        minLength: 6
        type: string
    required:
    - bulan
    - kode_server
    - metode
    - pembayaran
    - protocol
    - username
    type: object
  purchase.PurchaseTrialRequest:
    properties:
      kode_server:
        type: string
      protocol:
        enum:
        - trojan
        - vmess
        - vless
        type: string
    required:
    - kode_server
    - protocol
    type: object
  server.CreateServerRequest:
    properties:
      domain:
        type: string
      harga_member:
        minimum: 0
        type: integer
      harga_reseller:
        minimum: 0
        type: integer
      kode:
        description: Made optional - will be auto-generated if empty
        type: string
      max_device:
        minimum: 1
        type: integer
      nama:
        type: string
      nama_isp:
        type: string
      negara:
        type: string
      slot_server:
        minimum: 1
        type: integer
      slot_terpakai:
        type: integer
      ssh:
        type: string
      token:
        type: string
      total_user:
        type: integer
      trojan:
        type: string
      vless:
        type: string
      vmess:
        type: string
    required:
    - domain
    - harga_member
    - harga_reseller
    - max_device
    - nama
    - nama_isp
    - negara
    - slot_server
    - token
    type: object
  server.PublicServerListResponse:
    properties:
      servers:
        items:
          $ref: '#/definitions/server.PublicServerResponse'
        type: array
    type: object
  server.PublicServerResponse:
    properties:
      created_at:
        type: string
      domain:
        type: string
      harga_member:
        type: integer
      harga_reseller:
        type: integer
      kode:
        type: string
      max_device:
        type: integer
      nama:
        type: string
      nama_isp:
        type: string
      negara:
        type: string
      server_id:
        type: integer
      slot_server:
        type: integer
      slot_terpakai:
        type: integer
      ssh:
        type: string
      total_user:
        type: integer
      trojan:
        type: string
      updated_at:
        type: string
      vless:
        type: string
      vmess:
        type: string
    type: object
  server.SwaggerServerResponse:
    properties:
      created_at:
        type: string
      domain:
        type: string
      harga_member:
        type: integer
      harga_reseller:
        type: integer
      kode:
        type: string
      max_device:
        type: integer
      nama:
        type: string
      nama_isp:
        type: string
      negara:
        type: string
      server_id:
        type: integer
      slot_server:
        type: integer
      slot_terpakai:
        type: integer
      ssh:
        type: string
      token:
        type: string
      total_user:
        type: integer
      trojan:
        type: string
      updated_at:
        type: string
      vless:
        type: string
      vmess:
        type: string
    type: object
  server.TestTokenRequest:
    properties:
      api_path:
        type: string
      domain:
        type: string
      token:
        type: string
    required:
    - api_path
    - domain
    - token
    type: object
  server.UpdateServerRequest:
    properties:
      domain:
        type: string
      harga_member:
        minimum: 0
        type: integer
      harga_reseller:
        minimum: 0
        type: integer
      max_device:
        minimum: 1
        type: integer
      nama:
        type: string
      nama_isp:
        type: string
      negara:
        type: string
      slot_server:
        minimum: 1
        type: integer
      slot_terpakai:
        type: integer
      ssh:
        type: string
      token:
        type: string
      total_user:
        type: integer
      trojan:
        type: string
      vless:
        type: string
      vmess:
        type: string
    type: object
  shared.AccountDetailResponse:
    properties:
      account_type:
        type: string
      connection_links:
        items:
          $ref: '#/definitions/shared.ProcessedLink'
        type: array
      data_limit_gb:
        type: number
      expired_date:
        type: string
      protocol:
        type: string
      server:
        $ref: '#/definitions/shared.ServerInfoForAccountDetail'
      status:
        type: string
      subscription_url:
        type: string
      used_traffic_gb:
        type: number
      username:
        type: string
      uuid:
        type: string
    type: object
  shared.ErrorResponse:
    properties:
      details:
        description: Menggunakan interface{} agar bisa menampung berbagai jenis detail
          error
      error:
        type: string
    type: object
  shared.Pagination:
    properties:
      current_page:
        example: 1
        type: integer
      last_page:
        example: 10
        type: integer
      per_page:
        example: 10
        type: integer
      total:
        example: 100
        type: integer
    type: object
  shared.ProcessedLink:
    properties:
      name:
        type: string
      url:
        type: string
    type: object
  shared.RenewSuccessResponse:
    properties:
      message:
        example: Account renewed successfully
        type: string
      new_balance:
        example: 50000
        type: integer
      new_expire:
        example: "2024-12-31"
        type: string
    type: object
  shared.ServerInfoForAccountDetail:
    properties:
      created_at:
        type: string
      domain:
        type: string
      harga_member:
        type: integer
      harga_reseller:
        type: integer
      kode:
        type: string
      nama:
        type: string
      nama_isp:
        type: string
      negara:
        type: string
      server_id:
        type: integer
      ssh:
        type: string
      trojan:
        type: string
      updated_at:
        type: string
      vless:
        type: string
      vmess:
        type: string
    type: object
  shared.SuccessResponse:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  shared.TopUpResponse:
    properties:
      checkout_url:
        example: https://tripay.co.id/checkout/INV12345
        type: string
    type: object
  stats.TopServerResponse:
    properties:
      server_country:
        type: string
      server_kode:
        type: string
      server_name:
        type: string
      slot_server:
        type: integer
      slot_terpakai:
        type: integer
      total_user:
        type: integer
    type: object
  user.ChangePasswordRequest:
    properties:
      new_password:
        minLength: 6
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  user.PaginatedInvoicesResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/payment.Transaction'
        type: array
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
    type: object
  user.Role:
    properties:
      createdAt:
        type: string
      deletedAt:
        format: date-time
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      updatedAt:
        type: string
      users:
        items:
          $ref: '#/definitions/user.User'
        type: array
    type: object
  user.ServiceHistoryItem:
    properties:
      expired:
        description: Fields for invoice page (when clicking "Detail")
        type: string
      kode_akun:
        type: string
      kode_server:
        description: Server code for navigation to detail page
        type: string
      layanan:
        description: The server domain
        type: string
      nama_isp:
        description: The ISP name
        type: string
      nama_server:
        description: The server name
        type: string
      order_id:
        description: Unique identifier for the order/account
        type: string
      service_type:
        description: '"trojan", "vmess", "vless", "ssh"'
        type: string
      status:
        type: string
      tanggal_beli:
        description: Fields for table display
        type: string
      tipe:
        description: e.g., "trojan-monthly"
        type: string
      username:
        description: The account username (e.g., trial-xyz)
        type: string
    type: object
  user.ServiceHistoryResponse:
    properties:
      history:
        items:
          $ref: '#/definitions/user.ServiceHistoryItem'
        type: array
      limit:
        type: integer
      page:
        type: integer
      total:
        type: integer
    type: object
  user.SwaggerRoleResponse:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      updated_at:
        type: string
    type: object
  user.TransactionListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/user.TransactionResponse'
        type: array
      pagination:
        $ref: '#/definitions/shared.Pagination'
    type: object
  user.TransactionResponse:
    properties:
      amount:
        type: integer
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      invoice_id:
        type: string
      status:
        type: string
      type:
        type: string
      user_avatar:
        type: string
      user_name:
        type: string
    type: object
  user.UpdateProfileRequest:
    properties:
      email:
        type: string
      name:
        type: string
      whatsapp:
        type: string
    type: object
  user.UploadPhotoResponse:
    properties:
      message:
        type: string
      photo_url:
        type: string
    type: object
  user.User:
    properties:
      batasTrial:
        type: integer
      createdAt:
        type: string
      deleteHourlyCount:
        type: integer
      deletedAt:
        format: date-time
        type: string
      email:
        type: string
      emailVerified:
        type: integer
      emailVerifiedAt:
        type: string
      id:
        type: integer
      laba:
        type: integer
      lastDeleteHourlyAt:
        type: string
      lastLogin:
        type: string
      limitDeleteHourly:
        type: integer
      name:
        type: string
      notifLogin:
        type: integer
      otp:
        type: integer
      pathPhoto:
        type: string
      payBulanan:
        type: integer
      payHarian:
        type: integer
      payMingguan:
        type: integer
      payPerjam:
        type: integer
      roles:
        items:
          $ref: '#/definitions/user.Role'
        type: array
      saldo:
        type: integer
      suspend:
        type: string
      telegram:
        type: string
      totalPay:
        type: integer
      trial:
        type: integer
      updatedAt:
        type: string
      userTelegram:
        type: string
      username:
        type: string
      verifWa:
        type: integer
      whatsapp:
        type: string
    type: object
  user.UserStatsResponse:
    properties:
      batas_trial:
        type: integer
      jumlah_akun_hourly:
        type: integer
      jumlah_akun_month:
        type: integer
      jumlah_akun_ssh:
        type: integer
      jumlah_akun_trial:
        type: integer
      jumlah_akun_trojan:
        type: integer
      jumlah_akun_vless:
        type: integer
      jumlah_akun_vmess:
        type: integer
      pay_bulanan:
        type: integer
      total_account:
        type: integer
      total_pay:
        type: integer
      trial:
        type: integer
    type: object
  vpn-shop_backend-go_handlers_user.UserResponse:
    properties:
      account_type:
        type: string
      email:
        type: string
      email_verified:
        type: boolean
      email_verified_at:
        type: string
      id:
        type: integer
      name:
        type: string
      roles:
        items:
          $ref: '#/definitions/user.SwaggerRoleResponse'
        type: array
      saldo:
        type: integer
      username:
        type: string
      whatsapp:
        type: string
      whatsapp_verified:
        type: boolean
    type: object
host: localhost:8000
info:
  contact: {}
  description: This is the API for the VPN Shop application.
  title: VPN Shop API
  version: "1.0"
paths:
  /accounts/{account_id}:
    delete:
      consumes:
      - application/json
      description: Deletes a user's VPN account. Admins can delete any account, while
        regular users have an hourly limit. For hourly accounts, performs a final
        billing check.
      parameters:
      - description: Account ID
        in: path
        name: account_id
        required: true
        type: integer
      - description: Account Type
        enum:
        - trojan
        - vmess
        - vless
        - ssh
        in: query
        name: account_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - OAuth2Password: []
      summary: Delete a user account
      tags:
      - Accounts
  /accounts/{account_id}/activate:
    put:
      consumes:
      - application/json
      description: Sets an account's status to 'active' in both the local database
        and on the Marzban server.
      parameters:
      - description: Account ID
        in: path
        name: account_id
        required: true
        type: string
      - description: Account Type
        enum:
        - trojan
        - vmess
        - vless
        - ssh
        in: query
        name: account_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - OAuth2Password: []
      summary: Activate a user's account
      tags:
      - Accounts
  /accounts/{account_id}/disable:
    put:
      consumes:
      - application/json
      description: Sets an account's status to 'disabled' on the Marzban server and
        'suspended' in the local database.
      parameters:
      - description: Account ID
        in: path
        name: account_id
        required: true
        type: string
      - description: Account Type
        enum:
        - trojan
        - vmess
        - vless
        - ssh
        in: query
        name: account_type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - OAuth2Password: []
      summary: Disable a user's account
      tags:
      - Accounts
  /accounts/detail/{username}/{server_code}:
    get:
      consumes:
      - application/json
      description: Retrieves detailed information for a specific VPN account, combining
        local data and live data from the Marzban API.
      parameters:
      - description: Account Username
        in: path
        name: username
        required: true
        type: string
      - description: Server Code
        in: path
        name: server_code
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.AccountDetailResponse'
        "404":
          description: Server or account not found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get account detail
      tags:
      - Accounts
  /accounts/history:
    get:
      description: Get a list of all purchased services (Trojan, Vmess, Vless, SSH)
        for the logged-in user.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.ServiceHistoryResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user's service history
      tags:
      - Accounts
  /accounts/renew:
    post:
      consumes:
      - application/json
      description: Renews a user's VPN account for a specified number of months.
      parameters:
      - description: Renew Request
        in: body
        name: renew_request
        required: true
        schema:
          $ref: '#/definitions/account.RenewRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.RenewSuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "422":
          description: Unprocessable Entity
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - OAuth2Password: []
      summary: Renew a VPN account
      tags:
      - Accounts
  /admin/announcements:
    post:
      consumes:
      - application/json
      description: Membuat pengumuman baru dengan judul dan isi.
      parameters:
      - description: Data Pengumuman
        in: body
        name: announcement
        required: true
        schema:
          $ref: '#/definitions/admin.CreateAnnouncementRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/announcement.AnnouncementResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create Announcement
      tags:
      - Admin
  /admin/announcements/{id}:
    delete:
      description: Menghapus pengumuman berdasarkan ID.
      parameters:
      - description: Announcement ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete Announcement
      tags:
      - Admin
    put:
      consumes:
      - application/json
      description: Memperbarui judul atau isi dari pengumuman yang ada berdasarkan
        ID.
      parameters:
      - description: Announcement ID
        in: path
        name: id
        required: true
        type: integer
      - description: Data Pengumuman yang Diperbarui
        in: body
        name: announcement
        required: true
        schema:
          $ref: '#/definitions/admin.UpdateAnnouncementRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/announcement.AnnouncementResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update Announcement
      tags:
      - Admin
  /admin/settings:
    get:
      description: Retrieves the current application-wide settings.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/admin.AdminSetting'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Admin Settings
      tags:
      - Admin
    put:
      consumes:
      - application/json
      description: Updates the application-wide settings. All fields are required.
      parameters:
      - description: New settings data
        in: body
        name: settings
        required: true
        schema:
          $ref: '#/definitions/admin.AdminSetting'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/admin.AdminSetting'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update Admin Settings
      tags:
      - Admin
  /admin/users:
    get:
      description: Get a list of all users
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/admin.SwaggerUserListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all users
      tags:
      - Admin
  /admin/users/{id}:
    delete:
      description: Soft delete a user by their ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete a user
      tags:
      - Admin
    put:
      consumes:
      - application/json
      description: Update a user's details by their ID
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: User data to update
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/admin.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/admin.SwaggerUserDetailResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update a user
      tags:
      - Admin
  /announcements:
    get:
      description: Mengambil daftar semua pengumuman yang tersedia.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/announcement.AnnouncementResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Get All Announcements
      tags:
      - Public
  /announcements/{id}:
    get:
      description: Mengambil detail satu pengumuman berdasarkan ID-nya.
      parameters:
      - description: Announcement ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/announcement.AnnouncementResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Get Announcement By ID
      tags:
      - Public
  /auth/login:
    post:
      consumes:
      - application/x-www-form-urlencoded
      - application/json
      description: Authenticate a user and get a token. Used by Swagger's OAuth2 flow.
      parameters:
      - description: Username or Email
        in: formData
        name: identifier
        required: true
        type: string
      - description: Password
        in: formData
        name: password
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: '{\"token\": \"...\"}'
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Log in a user
      tags:
      - auth
  /auth/register:
    post:
      consumes:
      - application/json
      description: Create a new user account with username, email, and password.
      parameters:
      - description: User registration info
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/auth.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Register a new user
      tags:
      - auth
  /auth/telegram:
    post:
      consumes:
      - application/json
      description: Authenticates a user with a Telegram ID. If the user doesn't exist,
        it creates a new account.
      parameters:
      - description: Telegram User auth info
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/auth.TelegramAuthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "201":
          description: Created
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Authenticate or Register a user via Telegram
      tags:
      - auth
  /payment:
    post:
      consumes:
      - application/json
      description: Creates a new payment transaction for purchasing VPN products.
      parameters:
      - description: Payment Request
        in: body
        name: payment_request
        required: true
        schema:
          $ref: '#/definitions/payment.CreatePaymentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.TopUpResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create Product Payment
      tags:
      - payments
  /payment/channels:
    get:
      description: Retrieves a list of available payment channels from the payment
        gateway.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/payment.TripayChannelsResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Payment Channels
      tags:
      - payments
  /payment/status/{reference}:
    get:
      description: Checks the payment status of a transaction using Tripay API
      parameters:
      - description: Transaction Reference/Invoice ID
        in: path
        name: reference
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/payment.TripayTransactionStatus'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Check Payment Status
      tags:
      - payments
  /payment/topup:
    post:
      consumes:
      - application/json
      description: Initiates a new top-up transaction for the authenticated user.
      parameters:
      - description: Top-Up Request
        in: body
        name: topup_request
        required: true
        schema:
          $ref: '#/definitions/payment.TopUpRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.TopUpResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Top-Up Balance
      tags:
      - payments
  /public/transactions:
    get:
      description: Mengambil daftar transaksi publik. Tipe bisa 'topup', 'purchase',
        atau 'trial'.
      parameters:
      - description: Transaction Type
        enum:
        - topup
        - purchase
        - trial
        in: query
        name: type
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/public.TransactionResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Get Public Transactions
      tags:
      - Public
  /purchase/hourly:
    post:
      consumes:
      - application/json
      description: Memproses pembelian akun VPN per jam baru.
      parameters:
      - description: Data Pembelian Per Jam
        in: body
        name: purchase
        required: true
        schema:
          $ref: '#/definitions/purchase.PurchaseHourlyRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "402":
          description: Saldo tidak mencukupi
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Server tidak ditemukan
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Username sudah ada atau protokol tidak aktif
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Buy hourly accounts
      tags:
      - Purchase
  /purchase/monthly:
    post:
      consumes:
      - application/json
      description: Allows an authenticated user to purchase a new monthly VPN account.
      parameters:
      - description: Purchase Request
        in: body
        name: purchase_request
        required: true
        schema:
          $ref: '#/definitions/purchase.PurchaseMonthlyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/shared.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Purchase Monthly VPN Account
      tags:
      - Purchase
  /purchase/trial:
    post:
      consumes:
      - application/json
      description: Processes a new trial VPN account purchase. Limited to one per
        user. Username is auto-generated.
      parameters:
      - description: Trial Purchase Data
        in: body
        name: purchase
        required: true
        schema:
          $ref: '#/definitions/purchase.PurchaseTrialRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Jatah trial sudah habis
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Server tidak ditemukan
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Layanan tidak tersedia atau tidak dapat membuat username unik
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new trial account purchase record
      tags:
      - Purchase
  /servers:
    get:
      description: Get a list of all VPS servers
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/server.PublicServerListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get all servers
      tags:
      - server
    post:
      consumes:
      - application/json
      description: Create a new VPS server with detailed information
      parameters:
      - description: Server data
        in: body
        name: server
        required: true
        schema:
          $ref: '#/definitions/server.CreateServerRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/server.SwaggerServerResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Kode already exists
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create a new server
      tags:
      - server
  /servers/{id}:
    delete:
      description: Soft delete a VPS server by ID
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete a server
      tags:
      - server
    get:
      description: Get detailed information about a specific VPS server
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/server.PublicServerResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get a server by ID
      tags:
      - server
    put:
      consumes:
      - application/json
      description: Update information for an existing VPS server
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      - description: Server data to update
        in: body
        name: server
        required: true
        schema:
          $ref: '#/definitions/server.UpdateServerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/server.SwaggerServerResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update a server
      tags:
      - server
  /servers/{id}/test-token:
    get:
      description: Tests the connection to a server by making a request to its admin
        API with the stored token.
      parameters:
      - description: Server ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Successful response from the server
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid ServerID
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Server not found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal server error or failed to connect to the server
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Test server token
      tags:
      - server
  /servers/migrate:
    post:
      description: Migrate data from legacy my_servers table to the new server model
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Migrate legacy server data
      tags:
      - server
  /servers/test-token:
    post:
      consumes:
      - application/json
      description: Tests the connection to a server using provided domain and token
        without saving to database.
      parameters:
      - description: Domain and token to test
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.TestTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successful response from the server
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request data
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal server error or failed to connect to the server
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Test new server token
      tags:
      - server
  /stats/top-servers:
    get:
      description: Mengambil daftar 5 server teratas yang paling banyak dibeli secara
        global.
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/stats.TopServerResponse'
            type: array
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      summary: Get Top Purchase Servers
      tags:
      - Public
  /users/me:
    get:
      description: Get the profile of the currently logged-in user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/vpn-shop_backend-go_handlers_user.UserResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user's profile
      tags:
      - User
    put:
      consumes:
      - application/json
      description: Update the profile of the currently logged-in user
      parameters:
      - description: User data to update
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/user.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/vpn-shop_backend-go_handlers_user.UserResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "409":
          description: Email already in use
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update current user's profile
      tags:
      - User
  /users/me/change-password:
    put:
      consumes:
      - application/json
      description: Allows the authenticated user to change their password
      parameters:
      - description: Old and new passwords
        in: body
        name: passwords
        required: true
        schema:
          $ref: '#/definitions/user.ChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Change user's password
      tags:
      - User
  /users/me/invoices:
    get:
      description: Get a list of all invoices for the currently logged-in user
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.PaginatedInvoicesResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user's invoices
      tags:
      - User
  /users/me/invoices/{invoice_id}:
    get:
      description: Retrieves detailed information for a specific invoice for the authenticated
        user.
      parameters:
      - description: Invoice ID
        in: path
        name: invoice_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/payment.Transaction'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Invoice not found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User Invoice Details
      tags:
      - User
  /users/me/photo:
    post:
      consumes:
      - multipart/form-data
      description: Upload a profile photo for the currently logged-in user
      parameters:
      - description: Photo file to upload
        in: formData
        name: photo
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.UploadPhotoResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "413":
          description: File too large
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "415":
          description: Unsupported file type
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Upload user profile photo
      tags:
      - User
  /users/me/stats:
    get:
      description: Get aggregated statistics like account counts for the currently
        logged-in user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.UserStatsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user's statistics
      tags:
      - User
  /users/me/transactions:
    get:
      description: Mengambil daftar transaksi pengguna yang sedang login dengan paginasi
        dan filter.
      parameters:
      - default: 1
        description: Halaman
        in: query
        name: page
        type: integer
      - default: 10
        description: Jumlah item per halaman
        in: query
        name: limit
        type: integer
      - description: Kata kunci pencarian
        in: query
        name: search
        type: string
      - description: Filter berdasarkan tipe transaksi (TOPUP, PURCHASE_MONTHLY, PURCHASE_HOURLY,
          TRIAL, RENEWAL, REFUND, BILLING, BILLED_HOURLY)
        in: query
        name: type
        type: string
      - description: Filter berdasarkan status transaksi (PENDING, SUCCESS, FAILED)
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.TransactionListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User Transactions
      tags:
      - User
  /users/me/transactions/{id}:
    get:
      description: Mengambil detail transaksi pengguna berdasarkan ID.
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/user.TransactionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/shared.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User Transaction By ID
      tags:
      - User
securityDefinitions:
  BearerAuth:
    description: '"Type ''Bearer '' followed by your JWT token to authorize."'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
