package main

import (
	"fmt"
	"log"
	"vpn-shop/backend-go/utils"
)

func main() {
	// Test membuat beberapa avatar dengan nama berbeda
	testUsers := []struct {
		ID   uint
		Name string
	}{
		{1, "<PERSON>"},
		{2, "<PERSON>"},
		{3, "<PERSON>"},
		{4, "<PERSON>"},
		{5, "<PERSON>"},
	}

	fmt.Println("Testing avatar generation...")

	for _, user := range testUsers {
		avatarPath, err := utils.GenerateColoredAvatar(user.ID, user.Name)
		if err != nil {
			log.Printf("Error generating avatar for %s: %v", user.Name, err)
		} else {
			fmt.Printf("✅ Avatar created for %s (ID: %d): %s\n", user.Name, user.ID, avatarPath)
		}
	}

	fmt.Println("\nAvatar generation test completed!")
}
