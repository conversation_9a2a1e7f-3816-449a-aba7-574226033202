#!/bin/bash

# Script untuk testing endpoint upload foto
# Pastikan server sudah berjalan sebelum menjalankan script ini

# Warna untuk output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Testing Upload Photo Endpoint ===${NC}"

# Cek apakah server berjalan
echo -e "${YELLOW}Checking if server is running...${NC}"
if ! curl -s http://localhost:8000/ > /dev/null; then
    echo -e "${RED}Error: Server tidak berjalan di localhost:8000${NC}"
    echo -e "${YELLOW}Jalankan server terlebih dahulu dengan: go run main.go${NC}"
    exit 1
fi

echo -e "${GREEN}Server is running!${NC}"

# Buat file test image sederhana (1x1 pixel PNG)
echo -e "${YELLOW}Creating test image...${NC}"
echo -n -e '\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90\x77\x53\xde\x00\x00\x00\x0c\x49\x44\x41\x54\x08\x99\x01\x01\x00\x00\xff\xff\x00\x00\x00\x02\x00\x01\x73\x75\x01\x18\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82' > test_image.png

echo -e "${GREEN}Test image created: test_image.png${NC}"

# Test tanpa token (harus gagal)
echo -e "\n${YELLOW}Test 1: Upload without token (should fail)${NC}"
response=$(curl -s -w "%{http_code}" -X POST \
  -F "photo=@test_image.png" \
  http://localhost:8000/api/v1/users/me/photo)

http_code="${response: -3}"
if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ Test 1 passed: Correctly rejected request without token${NC}"
else
    echo -e "${RED}✗ Test 1 failed: Expected 401, got $http_code${NC}"
fi

# Test dengan token invalid (harus gagal)
echo -e "\n${YELLOW}Test 2: Upload with invalid token (should fail)${NC}"
response=$(curl -s -w "%{http_code}" -X POST \
  -H "Authorization: Bearer invalid_token" \
  -F "photo=@test_image.png" \
  http://localhost:8000/api/v1/users/me/photo)

http_code="${response: -3}"
if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ Test 2 passed: Correctly rejected request with invalid token${NC}"
else
    echo -e "${RED}✗ Test 2 failed: Expected 401, got $http_code${NC}"
fi

# Test dengan file yang bukan image (harus gagal)
echo -e "\n${YELLOW}Test 3: Upload non-image file (should fail)${NC}"
echo "This is not an image" > test_text.txt
response=$(curl -s -w "%{http_code}" -X POST \
  -H "Authorization: Bearer invalid_token" \
  -F "photo=@test_text.txt" \
  http://localhost:8000/api/v1/users/me/photo)

http_code="${response: -3}"
if [ "$http_code" = "401" ] || [ "$http_code" = "415" ]; then
    echo -e "${GREEN}✓ Test 3 passed: Correctly rejected non-image file${NC}"
else
    echo -e "${RED}✗ Test 3 failed: Expected 401 or 415, got $http_code${NC}"
fi

# Cleanup
rm -f test_image.png test_text.txt

echo -e "\n${YELLOW}=== Testing Complete ===${NC}"
echo -e "${YELLOW}Note: Untuk test dengan token valid, Anda perlu:${NC}"
echo -e "${YELLOW}1. Login terlebih dahulu untuk mendapatkan token${NC}"
echo -e "${YELLOW}2. Gunakan token tersebut untuk test upload${NC}"
echo -e "${YELLOW}3. Contoh: curl -H \"Authorization: Bearer YOUR_TOKEN\" -F \"photo=@image.jpg\" http://localhost:8000/api/v1/users/me/photo${NC}"
