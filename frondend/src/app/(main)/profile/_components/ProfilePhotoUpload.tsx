"use client";

import Image from "next/image";
import { useState } from "react";
import { ApiClient } from "@/lib/apiClientEnhanced";

import type { UserProfile } from "@/types/user";

// Assuming CameraIcon exists from the previous implementation
const CameraIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.3636 4.25H11.5L10.25 2.5H5.75L4.5 4.25H2.63636C2.13636 4.25 1.75 4.63636 1.75 5.13636V12.8636C1.75 13.3636 2.13636 13.75 2.63636 13.75H13.3636C13.8636 13.75 14.25 13.3636 14.25 12.8636V5.13636C14.25 4.63636 13.8636 4.25 13.3636 4.25Z"
      stroke="currentColor"
      strokeLinejoin="round"
    />
    <path
      d="M8 11.125C9.44975 11.125 10.625 9.94975 10.625 8.5C10.625 7.05025 9.44975 5.875 8 5.875C6.55025 5.875 5.375 7.05025 5.375 8.5C5.375 9.94975 6.55025 11.125 8 11.125Z"
      stroke="currentColor"
      strokeLinejoin="round"
    />
  </svg>
);

interface ProfilePhotoUploadProps {
  user: UserProfile;
  accessToken: string;
  onPhotoUpdate?: (newPhotoUrl: string) => void;
}

export default function ProfilePhotoUpload({ user, accessToken, onPhotoUpdate }: ProfilePhotoUploadProps) {
  const [preview, setPreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Reset previous errors
      setError(null);
      setSuccess(null);

      // Validate file size (3MB = 3 * 1024 * 1024 bytes)
      const maxSize = 3 * 1024 * 1024;
      if (file.size > maxSize) {
        setError('Ukuran file terlalu besar. Maksimal 3MB');
        return;
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError('Tipe file tidak didukung. Hanya JPEG, PNG, dan WebP yang diizinkan');
        return;
      }

      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    const formData = new FormData();
    formData.append("photo", selectedFile);

    try {
      const apiClient = new ApiClient();
      const response = await apiClient.post('/users/me/photo', formData, {
        token: accessToken,
        headers: {
          // Don't set Content-Type for FormData, let the browser set it
        }
      });

      console.log('Full response object:', response);
      console.log('Response keys:', Object.keys(response));

      // Handle successful response
      if (response.data && response.data.message) {
        setSuccess(response.data.message);
      } else if (response.message) {
        setSuccess(response.message);
      } else {
        setSuccess('Foto profil berhasil diperbarui!');
      }

      // Update parent component with new photo URL
      console.log('Upload response:', response.data);
      console.log('onPhotoUpdate callback:', onPhotoUpdate);

      if (response.data && response.data.photo_url && onPhotoUpdate) {
        console.log('Calling onPhotoUpdate with photo_url:', response.data.photo_url);
        const apiClient = new ApiClient();
        const fullPhotoUrl = apiClient.getPhotoUrl(response.data.photo_url, true); // Cache busting
        console.log('Full photo URL:', fullPhotoUrl);
        onPhotoUpdate(fullPhotoUrl);
      } else {
        console.log('Condition failed:', {
          hasResponseData: !!response.data,
          hasPhotoUrl: !!(response.data && response.data.photo_url),
          hasCallback: !!onPhotoUpdate
        });
      }

      setSelectedFile(null);
      setPreview(null);

      // Optional: Still reload after a delay if no callback provided
      if (!onPhotoUpdate) {
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      }
    } catch (err: any) {
      // Handle error response
      if (err.response && err.response.data && err.response.data.error) {
        setError(err.response.data.error);
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('Terjadi kesalahan saat mengupload foto');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="form-card">
      {/* Header */}
      <div className="form-card-header">
        <h3 className="text-lg font-semibold">Foto Profil</h3>
        <p className="text-sm text-dark-6 mt-1">Perbarui foto profil Anda</p>
      </div>

      <div className="form-card-body">
        <div className="flex flex-col items-center gap-6">
          {/* Profile Photo Section */}
          <div className="relative">
            <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-stroke bg-gray-2 dark:border-dark-3 dark:bg-dark-2">
              <Image
                src={preview || user.profilePhoto || "/images/user/user-01.png"}
                alt="User"
                width={128}
                height={128}
                className="h-full w-full object-cover transition-all duration-300 hover:scale-105"
              />
            </div>
            {/* Camera Icon positioned outside the photo */}
            <label
              htmlFor="profile"
              className="absolute -bottom-2 -right-2 flex h-12 w-12 cursor-pointer items-center justify-center rounded-full bg-primary text-white shadow-xl border-4 border-white dark:border-gray-dark transition-all duration-300 hover:bg-primary/90 hover:scale-110 hover:shadow-2xl"
            >
              <CameraIcon />
              <input
                type="file"
                name="profile"
                id="profile"
                className="form-file-input sr-only"
                onChange={handleFileChange}
                accept="image/png, image/jpg, image/jpeg, image/webp"
              />
            </label>
          </div>

          {/* Upload Actions */}
          <div className="w-full space-y-4">
            {error && (
              <div className="mb-4 rounded-lg border border-red/20 bg-red/10 p-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <svg className="h-4 w-4 text-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium text-red">{error}</span>
                </div>
              </div>
            )}
            
            {success && (
              <div className="mb-4 rounded-lg border border-green/20 bg-green/10 p-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <svg className="h-4 w-4 text-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm font-medium text-green">{success}</span>
                </div>
              </div>
            )}

            {selectedFile && (
              <div className="space-y-4">
                <div className="rounded-lg border border-stroke bg-gray-1 p-3 dark:border-dark-3 dark:bg-dark-2">
                  <p className="form-label mb-1">File Dipilih:</p>
                  <p className="text-xs text-dark-6">{selectedFile.name}</p>
                  <p className="text-xs text-dark-6">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                </div>
                
                <div className="flex gap-3">
                  <button
                    className="form-button-secondary flex-1"
                    type="button"
                    onClick={() => { setSelectedFile(null); setPreview(null); }}
                    disabled={loading}
                  >
                    Batal
                  </button>
                  <button
                    className="form-button-primary flex-1"
                    type="button"
                    onClick={handleUpload}
                    disabled={loading}
                  >
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                        Mengunggah...
                      </div>
                    ) : (
                      'Simpan Foto'
                    )}
                  </button>
                </div>
              </div>
            )}

            {!selectedFile && (
              <div className="text-center">
                <p className="text-xs text-dark-6 mb-2">Format yang didukung: PNG, JPG, JPEG, WebP</p>
                <p className="text-xs text-dark-6">Ukuran maksimal: 3MB</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
