"use client";

import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ApiClient } from "@/lib/apiClientEnhanced";

import SettingsContainer from "./_components/SettingsContainer";

import type { UserProfile } from "@/types/user";

// Interface untuk statistik tambahan dari API
interface UserStats {
  pay_bulanan: number;
  total_pay: number;
  batas_trial: number;
  trial: number;
  jumlah_akun_trial: number;
  jumlah_akun_hourly: number;
  jumlah_akun_month: number;
  jumlah_akun_trojan: number;
  jumlah_akun_vmess: number;
  jumlah_akun_vless: number;
  jumlah_akun_ssh: number;
  total_account: number;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [loadingProfile, setLoadingProfile] = useState(true);
  const [loadingStats, setLoadingStats] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect if not authenticated
    if (status === "unauthenticated") {
      router.push("/");
      return;
    }

    if (status === "authenticated") {
      const fetchUserProfile = async () => {
        setLoadingProfile(true);
        setError(null);
        try {
          // @ts-ignore: accessToken is a custom property on the session
          const token = session.accessToken;
          if (!token) {
            throw new Error("Access token not found in session.");
          }

          const apiClient = new ApiClient();
          
          // Fetch user profile dan stats secara bersamaan
          const [userProfileData, userStatsData] = await Promise.all([
            apiClient.get('/users/me', { token }),
            apiClient.get('/users/me/stats', { token })
          ]);
          
          // API returns the user object directly, not nested.
          // Provide default values for fields that might be missing.
          const processedProfile: UserProfile = {
            id: userProfileData.id,
            username: userProfileData.username,
            name: userProfileData.name,
            email: userProfileData.email,
            whatsapp: userProfileData.whatsapp || "",
            profilePhoto: userProfileData.photo_url
              ? `${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/${userProfileData.photo_url}`
              : "/images/user/user-03.png",
            emailVerified: userProfileData.email_verified,
            whatsappVerified: userProfileData.whatsapp_verified,
            accountType: userProfileData.account_type,
            emailVerifiedAt: userProfileData.email_verified_at,
            saldo: userProfileData.saldo || 0,
            roles: userProfileData.roles || [],
            pay_bulanan: userStatsData.pay_bulanan || 0,
            total_pay: userStatsData.total_pay || 0,
            batas_trial: userStatsData.batas_trial || 0,
            trial: userStatsData.trial || 0,
          };
          
          setProfile(processedProfile);
          setUserStats(userStatsData);

        } catch (err: any) {
          setError(err.message);
          // Fallback to mock data for UI development if API fails
          console.error("Failed to fetch profile, using mock data:", err);
          const mockUser: UserProfile = {
            // @ts-ignore
            id: session.user?.id || 0,
            // @ts-ignore
            username: session.user?.username || "user",
            name: session.user?.name || "User Name",
            email: session.user?.email || "<EMAIL>",
            whatsapp: "************",
            profilePhoto: "/images/user/user-03.png",
            emailVerified: true,
            whatsappVerified: false,
            accountType: "Premium",
            emailVerifiedAt: new Date().toISOString(),
            saldo: 150000,
            roles: [{ ID: 1, Name: 'User' }],
            pay_bulanan: 50000,
            total_pay: 250000,
            batas_trial: 5,
            trial: 1,
          };
          setProfile(mockUser);
        } finally {
          setLoadingProfile(false);
          setLoadingStats(false);
        }
      };

      fetchUserProfile();
    }
  }, [status, session, router]);





  if (!profile || !userStats) {
    return (
      <div className="mx-auto max-w-270">
        <Breadcrumb pageName="Profil Pengguna" />
        <div className="rounded-lg border border-danger bg-danger/10 p-6 text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-danger/20 flex items-center justify-center">
            <svg className="h-6 w-6 text-danger" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-danger mb-2">Gagal Memuat Profil</h3>
          <p className="text-danger/80">{error || "Terjadi kesalahan saat memuat data profil"}</p>
        </div>
      </div>
    );
  }

  if (!session || !profile) {
    return null;
  }

  return (
    <div className="mx-auto max-w-270">
      <Breadcrumb pageName="Profil Pengguna" />

      {/* Hero Section dengan Gradient Background */}
      <div className="relative mb-8 overflow-hidden rounded-2xl bg-gradient-to-br from-primary via-blue to-purple-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <div className="flex flex-col items-center text-center sm:flex-row sm:text-left">
            <div className="mb-4 sm:mb-0 sm:mr-6">
              <div className="relative h-24 w-24 overflow-hidden rounded-full border-4 border-white/20 bg-white/10 backdrop-blur-sm">
                <img
                  src={profile.profilePhoto}
                  alt={profile.name}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src = "/images/user/user-03.png";
                  }}
                />
              </div>
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">{profile.name}</h1>
              <p className="text-white/80 mb-1">@{profile.username}</p>
              <p className="text-white/70 text-sm">{profile.email}</p>
              <div className="mt-3 flex flex-wrap gap-2 justify-center sm:justify-start">
                <span className="rounded-full bg-white/20 px-3 py-1 text-xs font-medium backdrop-blur-sm">
                  {profile.accountType}
                </span>
                {profile.emailVerified && (
                  <span className="rounded-full bg-green/20 px-3 py-1 text-xs font-medium backdrop-blur-sm">
                    Email Terverifikasi
                  </span>
                )}
              </div>
            </div>
            <div className="mt-4 sm:mt-0 text-center">
              <div className="rounded-lg bg-white/10 p-4 backdrop-blur-sm">
                <p className="text-2xl font-bold">Rp {profile.saldo.toLocaleString("id-ID")}</p>
                <p className="text-white/80 text-sm">Saldo Tersedia</p>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Main Content */}
      <div className="w-full">
        <SettingsContainer user={profile} accessToken={session.accessToken as string} userStats={userStats} />
      </div>
    </div>
  );
}
