'use client';

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { ServerIcon } from '@/assets/icons'; // Placeholder icon

// Definisikan tipe data untuk server terpopuler
interface TopServer {
  server_kode: string;
  server_name: string;
  server_country: string;
  slot_server: number;
  slot_terpakai: number;
  total_user: number;
}

// Fungsi untuk mengambil data server terpopuler
async function getTopServers(): Promise<TopServer[]> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${apiUrl}/stats/top-servers`);
    if (!response.ok) {
      console.error('Gagal mengambil data server terpopuler, status:', response.status);
      return [];
    }
    const data = await response.json();
    return data || [];
  } catch (error) {
    console.error('Error saat fetching top servers:', error);
    return [];
  }
}

// Fungsi untuk mendapatkan emoji bendera berdasarkan nama negara (contoh sederhana)
function getCountryFlag(country: string): string {
  switch (country.toLowerCase()) {
    case 'singapore':
      return '🇸🇬';
    case 'indonesia':
      return '🇮🇩';
    case 'united states':
      return '🇺🇸';
    default:
      return '🏳️'; // Bendera putih sebagai default
  }
}

// Fungsi untuk mendapatkan medali berdasarkan peringkat
function getMedal(rank: number): { emoji: string; gradient: string; textColor: string } {
  if (rank === 0) return {
    emoji: '🥇',
    gradient: 'bg-gradient-to-r from-yellow-400 to-yellow-600',
    textColor: 'text-yellow-900'
  };
  if (rank === 1) return {
    emoji: '🥈',
    gradient: 'bg-gradient-to-r from-gray-300 to-gray-500',
    textColor: 'text-gray-800'
  };
  if (rank === 2) return {
    emoji: '🥉',
    gradient: 'bg-gradient-to-r from-orange-400 to-orange-600',
    textColor: 'text-orange-900'
  };
  return {
    emoji: `${rank + 1}`,
    gradient: 'bg-gradient-to-r from-blue-400 to-blue-600',
    textColor: 'text-blue-900'
  };
}

// Fungsi untuk mendapatkan warna progress bar berdasarkan persentase
function getProgressColor(percentage: number): string {
  if (percentage >= 80) return 'bg-gradient-to-r from-red-500 to-red-600';
  if (percentage >= 60) return 'bg-gradient-to-r from-orange-500 to-orange-600';
  if (percentage >= 40) return 'bg-gradient-to-r from-yellow-500 to-yellow-600';
  return 'bg-gradient-to-r from-green-500 to-green-600';
}

export function TopServers({ className }: { className?: string }) {
  const [topServers, setTopServers] = useState<TopServer[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadTopServers() {
      setLoading(true);
      const data = await getTopServers();
      setTopServers(data);
      setLoading(false);
    }
    loadTopServers();
  }, []);

  

  return (
    <div
      className={cn(
        "grid gap-4 rounded-xl bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      {/* Modern Header dengan Gradient */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-600 p-4 text-white mb-2">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -right-4 -top-4 h-16 w-16 rounded-full bg-white/10 backdrop-blur-sm"></div>
        <div className="absolute -left-2 -bottom-2 h-12 w-12 rounded-full bg-white/5 backdrop-blur-sm"></div>

        <div className="relative z-10">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
              <ServerIcon className="h-5 w-5" />
            </div>
            <h2 className="text-xl font-bold">Server Terpopuler</h2>
          </div>
          <p className="text-white/80 text-sm mt-1">Berdasarkan penggunaan slot</p>
        </div>
      </div>

      <div className="h-[350px] overflow-y-auto pr-2 custom-scrollbar">
        {loading ? (
          <div className="flex h-full items-center justify-center">
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-emerald-500/10 rounded-full">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-emerald-500 border-t-transparent"></div>
              <span className="text-emerald-600 font-medium">Memuat data server...</span>
            </div>
          </div>
        ) : topServers.length > 0 ? (
          <ul className="space-y-4">
            {topServers.map((server, index) => {
              const slotPercentage = server.slot_server > 0 ? (server.slot_terpakai / server.slot_server) * 100 : 0;
              return (
                <li
                  key={server.server_kode}
                  className="group relative overflow-hidden rounded-xl border border-gray-200 dark:border-gray-700 bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 p-4 transition-all duration-300 hover:shadow-lg hover:border-primary/30 hover:-translate-y-1"
                >
                  {/* Background Progress Bar */}
                  <div className="absolute inset-0 opacity-5">
                    <div
                      className={`h-full transition-all duration-1000 ease-out ${getProgressColor(slotPercentage)}`}
                      style={{ width: `${slotPercentage}%` }}
                    ></div>
                  </div>

                  <div className="relative z-10">
                    {/* Header Row - Medal, Flag, Name, Status Badge */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className={`flex h-10 w-10 items-center justify-center rounded-full ${getMedal(index).gradient} shadow-lg transition-transform duration-300 group-hover:scale-110`}>
                          <span className={`text-lg font-bold ${getMedal(index).textColor}`}>
                            {getMedal(index).emoji}
                          </span>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className="text-xl">{getCountryFlag(server.server_country)}</span>
                          <h3 className="font-bold text-gray-900 dark:text-white truncate" title={server.server_name}>
                            {server.server_name}
                          </h3>
                        </div>
                      </div>

                      {/* Status Badge - Always visible on right */}
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-semibold ${
                        slotPercentage >= 90 ? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300' :
                        slotPercentage >= 70 ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300' :
                        'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                      }`}>
                        <div className={`w-2 h-2 rounded-full ${
                          slotPercentage >= 90 ? 'bg-red-500' :
                          slotPercentage >= 70 ? 'bg-orange-500' :
                          'bg-green-500'
                        }`}></div>
                        {slotPercentage >= 90 ? 'Penuh' : slotPercentage >= 70 ? 'Tinggi' : 'Normal'}
                      </div>
                    </div>

                    {/* Progress Row - Full width below */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                          Slot Usage
                        </span>
                        <span className="text-xs font-bold text-gray-800 dark:text-gray-200">
                          {server.slot_terpakai} / {server.slot_server}
                        </span>
                      </div>

                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full transition-all duration-1000 ease-out ${getProgressColor(slotPercentage)}`}
                          style={{ width: `${slotPercentage}%` }}
                        ></div>
                      </div>

                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {slotPercentage.toFixed(1)}% terpakai
                      </p>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <div className="flex h-full flex-col items-center justify-center text-center">
            <div className="text-6xl mb-4 animate-bounce">📊</div>
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
              Belum Ada Data
            </h3>
            <p className="text-gray-500 dark:text-gray-500 text-sm">
              Data server populer akan muncul setelah ada aktivitas
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
