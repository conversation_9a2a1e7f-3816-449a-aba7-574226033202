"use client";

import { ChevronUpIcon } from "@/assets/icons";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { cn } from "@/lib/utils";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { UserProfile } from "@/types/user";
import { ApiClient } from "@/lib/apiClientEnhanced";
import { LogOutIcon, SettingsIcon, UserIcon } from "./icons";

export function UserInfo() {
  const { data: session, status } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [userInfo, setUserInfo] = useState<Partial<UserProfile> | null>(null);

  useEffect(() => {
    if (status === "authenticated" && session?.accessToken) {
      const fetchUserInfo = async () => {
        try {
          const apiClient = new ApiClient();
          // @ts-ignore
          const data = await apiClient.get('/users/me', { token: session.accessToken });

          // Map photo_url to profilePhoto for consistency
          const processedData = {
            ...data,
            profilePhoto: data.photo_url
              ? `${process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'}/${data.photo_url}`
              : null
          };

          setUserInfo(processedData);
        } catch (error) {
          console.error("Failed to fetch user info for header:", error);
        }
      };

      fetchUserInfo();
    }
  }, [status, session]);

  if (status === "loading") {
    return (
      <div className="h-12 w-48 animate-pulse rounded-md bg-gray-2 dark:bg-dark-3"></div>
    );
  }

  if (status === "unauthenticated" || !session?.user) {
    return null; // Don't show the component if the user is not logged in
  }

  const user = {
    name: userInfo?.name || session.user.name || "User",
    email: userInfo?.email || session.user.email || "No email provided",
    img: userInfo?.profilePhoto || session.user.image || "/images/user/user-03.png", // A fallback image
  };

  return (
    <Dropdown isOpen={isOpen} setIsOpen={setIsOpen}>
      <DropdownTrigger className="rounded align-middle outline-none ring-primary ring-offset-2 focus-visible:ring-1 dark:ring-offset-gray-dark">
        <span className="sr-only">My Account</span>

        <figure className="flex items-center gap-3">
          <Image
            src={user.img}
            className="size-12 rounded-full"
            alt={`Avatar of ${user.name}`}
            role="presentation"
            width={48}
            height={48}
          />
          <figcaption className="flex items-center gap-1 font-medium text-dark dark:text-dark-6 max-[1024px]:sr-only">
            <span>{user.name}</span>

            <ChevronUpIcon
              aria-hidden
              className={cn(
                "rotate-180 transition-transform",
                isOpen && "rotate-0",
              )}
              strokeWidth={1.5}
            />
          </figcaption>
        </figure>
      </DropdownTrigger>

      <DropdownContent
        className="border border-stroke bg-white shadow-md dark:border-dark-3 dark:bg-gray-dark min-[230px]:min-w-[17.5rem]"
        align="end"
      >
        <h2 className="sr-only">User information</h2>

        <figure className="flex items-center gap-2.5 px-5 py-3.5">
          <Image
            src={user.img}
            className="size-12 rounded-full"
            alt={`Avatar for ${user.name}`}
            role="presentation"
            width={48}
            height={48}
          />

          <figcaption className="space-y-1 text-base font-medium">
            <div className="mb-2 leading-none text-dark dark:text-white">
              {user.name}
            </div>

            <div className="truncate text-sm leading-none text-gray-6">{user.email}</div>
          </figcaption>
        </figure>

        <hr className="border-[#E8E8E8] dark:border-dark-3" />

        <div className="p-2 text-base text-[#4B5563] dark:text-dark-6 [&>*]:cursor-pointer">
          <Link
            href={"/profile"}
            onClick={() => setIsOpen(false)}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
          >
            <UserIcon />

            <span className="mr-auto text-base font-medium">View profile</span>
          </Link>

          <Link
            href={"/pages/settings"}
            onClick={() => setIsOpen(false)}
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
          >
            <SettingsIcon />

            <span className="mr-auto text-base font-medium">
              Account Settings
            </span>
          </Link>
        </div>

        <hr className="border-[#E8E8E8] dark:border-dark-3" />

        <div className="p-2 text-base text-[#4B5563] dark:text-dark-6">
          <button
            className="flex w-full items-center gap-2.5 rounded-lg px-2.5 py-[9px] hover:bg-gray-2 hover:text-dark dark:hover:bg-dark-3 dark:hover:text-white"
            onClick={() => signOut({ callbackUrl: "/" })}
          >
            <LogOutIcon />

            <span className="text-base font-medium">Log out</span>
          </button>
        </div>
      </DropdownContent>
    </Dropdown>
  );
}
